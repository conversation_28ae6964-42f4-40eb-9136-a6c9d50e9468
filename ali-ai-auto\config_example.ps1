# Mobile-Agent-E 配置文件
# 已配置好的API密钥

# ADB路径 (已自动检测)
$env:ADB_PATH = "C:\apps\adb\platform-tools\adb.exe"

# 选择后端模型 (OpenAI, Gemini, Claude)
$env:BACKBONE_TYPE = "Gemini"

# API密钥设置 - 请取消注释并填入你的密钥

# === 必须设置 ===
# Qwen API密钥 (用于图标识别，必需)
$env:QWEN_API_KEY = "sk-a087535b2bc749f1aee28526cd151e7a"

# === 推理模型 (三选一) ===
# 选项1: OpenAI (推荐)
# $env:OPENAI_API_KEY = "sk-your-openai-api-key-here"

# 选项2: Gemini (当前使用)
$env:GEMINI_API_KEY = "AIzaSyBEI8KcpvNBwCvXjUJ3lfuxrA12wnlVKu0"

# 选项3: Claude
# $env:CLAUDE_API_KEY = "your-claude-api-key-here"

Write-Host "配置已加载" -ForegroundColor Green
Write-Host "当前设置:"
Write-Host "- ADB_PATH: $env:ADB_PATH"
Write-Host "- BACKBONE_TYPE: $env:BACKBONE_TYPE"

# 检查必须的Qwen API密钥
if ($env:QWEN_API_KEY) {
    Write-Host "- Qwen API Key: 已设置 ✅" -ForegroundColor Green
} else {
    Write-Host "- Qwen API Key: 未设置 ❌ (必需)" -ForegroundColor Red
}

# 检查推理模型API密钥 (三选一)
$reasoning_api_set = $false
if ($env:OPENAI_API_KEY) {
    Write-Host "- OpenAI API Key: 已设置 ✅" -ForegroundColor Green
    $reasoning_api_set = $true
}
if ($env:GEMINI_API_KEY) {
    Write-Host "- Gemini API Key: 已设置 ✅" -ForegroundColor Green
    $reasoning_api_set = $true
}
if ($env:CLAUDE_API_KEY) {
    Write-Host "- Claude API Key: 已设置 ✅" -ForegroundColor Green
    $reasoning_api_set = $true
}

if (-not $reasoning_api_set) {
    Write-Host "- 推理模型API: 未设置 ❌ (需要OpenAI/Gemini/Claude其中一个)" -ForegroundColor Red
}

Write-Host ""
if ($env:QWEN_API_KEY -and $reasoning_api_set) {
    Write-Host "🎉 所有必需的API密钥已设置，可以开始使用！" -ForegroundColor Green
} else {
    Write-Host "⚠️  请设置必需的API密钥后再运行任务" -ForegroundColor Yellow
}
