# Mobile-Agent-E 演示运行脚本

# 设置环境变量
$env:ADB_PATH = "C:\apps\adb\platform-tools\adb.exe"
$env:BACKBONE_TYPE = "OpenAI"

# 注意：在实际运行前，请设置相应的API密钥
# $env:OPENAI_API_KEY = "your-openai-api-key-here"
# $env:QWEN_API_KEY = "your-qwen-api-key-here"

Write-Host "=== Mobile-Agent-E 演示 ===" -ForegroundColor Green
Write-Host "环境变量已设置:" -ForegroundColor Yellow
Write-Host "ADB_PATH = $env:ADB_PATH"
Write-Host "BACKBONE_TYPE = $env:BACKBONE_TYPE"
Write-Host ""

# 检查ADB连接
Write-Host "检查ADB连接..." -ForegroundColor Yellow
& $env:ADB_PATH devices
Write-Host ""

# 提示用户设置API密钥
if (-not $env:OPENAI_API_KEY) {
    Write-Host "警告: 未设置 OPENAI_API_KEY" -ForegroundColor Red
    Write-Host "请设置API密钥后再运行任务" -ForegroundColor Red
    Write-Host ""
}

if (-not $env:QWEN_API_KEY) {
    Write-Host "警告: 未设置 QWEN_API_KEY (图标识别需要)" -ForegroundColor Red
    Write-Host "请设置API密钥后再运行任务" -ForegroundColor Red
    Write-Host ""
}

Write-Host "可用的运行选项:" -ForegroundColor Green
Write-Host "1. 运行单个任务示例:"
Write-Host "   python run.py --instruction `"打开设置应用`" --run_name `"test_settings`""
Write-Host ""
Write-Host "2. 运行预定义的脚本任务:"
Write-Host "   bash scripts/run_task.sh"
Write-Host ""
Write-Host "3. 运行多任务演化模式:"
Write-Host "   bash scripts/run_tasks_evolution.sh"
Write-Host ""

# 询问用户是否要运行示例
$choice = Read-Host "是否要运行一个简单的测试任务? (y/n)"
if ($choice -eq "y" -or $choice -eq "Y") {
    if ($env:OPENAI_API_KEY -and $env:QWEN_API_KEY) {
        Write-Host "运行测试任务..." -ForegroundColor Green
        python run.py --instruction "打开设置应用并截图" --run_name "test_demo" --max_itr 5
    } else {
        Write-Host "请先设置API密钥再运行任务" -ForegroundColor Red
    }
} else {
    Write-Host "脚本结束。请根据需要手动运行相应命令。" -ForegroundColor Yellow
}
