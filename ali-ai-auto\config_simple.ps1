# Mobile-Agent-E Configuration

# ADB Path
$env:ADB_PATH = "C:\apps\adb\platform-tools\adb.exe"

# Backend Type (OpenAI, <PERSON>, <PERSON>, <PERSON>wen)
$env:BACKBONE_TYPE = "Qwen"

# API Keys
$env:QWEN_API_KEY = "sk-a087535b2bc749f1aee28526cd151e7a"  # 用于图标识别
$env:QWEN_REASONING_API_KEY = "sk-a087535b2bc749f1aee28526cd151e7a"  # 用于推理模型
$env:GEMINI_API_KEY = "AIzaSyBEI8KcpvNBwCvXjUJ3lfuxrA12wnlVKu0"

Write-Host "Configuration loaded successfully!" -ForegroundColor Green
Write-Host "ADB_PATH: $env:ADB_PATH"
Write-Host "BACKBONE_TYPE: $env:BACKBONE_TYPE"
Write-Host "QWEN_API_KEY: Set (for icon recognition)" -ForegroundColor Green
Write-Host "QWEN_REASONING_API_KEY: Set (for reasoning)" -ForegroundColor Green
Write-Host "DASHSCOPE_API_KEY: Set (for DashScope)" -ForegroundColor Green
Write-Host "GEMINI_API_KEY: Set (backup)" -ForegroundColor Green
Write-Host "Ready to run Mobile-Agent-E with Qwen reasoning model!" -ForegroundColor Green
